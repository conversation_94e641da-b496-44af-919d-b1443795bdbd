'use client';

import { But<PERSON>, Icon, Separator } from '@/src/app/_components';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { RefObject } from 'react';

import { useAnalyticsEventGeneric, useMenu, useSubmenu } from '@/src/app/_hooks';

// Importação dinâmica para DesktopNavigation
const DesktopNavigation = dynamic(() => import('./DesktopNavigation'));

// Importação dinâmica para MobileNavigation
const MobileNavigation = dynamic(() => import('./MobileNavigation'));

interface HeaderProps {
  logoPath: string;
  headerRef: RefObject<HTMLElement>;
  submenuRef: RefObject<HTMLDivElement>;
}

export default function Header({
  logoPath,
  headerRef,
  submenuRef,
}: HeaderProps): React.ReactElement {
  const pathname = usePathname();

  const { sendEvent } = useAnalyticsEventGeneric();
  const { isMenuOpen, toggleMenu } = useMenu();
  const { isSubmenuOpen, handleMouseEnter, handleMouseLeave } = useSubmenu(submenuRef);

  return (
    <header
      ref={headerRef}
      className="fixed top-0 z-[60] w-full bg-white shadow-[0px_1px_4px_0px_rgba(0,0,0,0.08)]"
    >
      {/* Domain Indicator Bar */}
      <div className="w-full bg-[#020618]">
        <div className="flex items-center justify-between px-4 py-2 md:px-8 2xl:px-40">
          {/* Logo Container */}
          <div className="flex items-center">
            <div className="flex items-center justify-center p-2">
              <Image
                src="/images/GetNinjas_logo_white.svg"
                alt="GetNinjas Logo"
                width={56}
                height={12}
                className="h-3 w-auto object-contain"
                quality={85}
                priority
              />
            </div>
          </div>

          {/* Header Links Container */}
          <div className="flex items-center gap-[18px]">
            {/* Clickable link to GetNinjas */}
            <Link
              href="https://getninjas.com.br"
              target="_blank"
              rel="noopener noreferrer"
              className="text-xs font-semibold text-[#F8FAFC] transition-colors hover:text-white"
            >
              Orçamentos com profissionais
            </Link>

            {/* Selected tab - current section */}
            <div className="flex min-w-[182px] items-center justify-center rounded-t-lg bg-white px-4 py-1">
              <span className="text-xs font-semibold text-[#020618]">
                Serviços com preço fechado
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header Content */}
      <div className="border-b border-gray-200">
        <div className="flex items-center justify-between px-4 py-6 md:px-8 2xl:px-40">
          <Link href="/" className="flex items-center">
            <Image
              src={logoPath || '/images/GetNinjas_logo.svg'}
              alt="GetNinjas Logo"
              width={120}
              height={40}
              className="h-7 w-auto object-contain"
              quality={85}
              priority
            />
            <Separator orientation="vertical" className="mx-3 h-6 w-[1.5px]" />
            <h2 className="text-lg font-bold">Preço Fechado</h2>
          </Link>

          {/* Desktop Navigation */}
          <DesktopNavigation
            submenuRef={submenuRef as RefObject<HTMLDivElement>}
            isSubmenuOpen={isSubmenuOpen}
            handleMouseEnter={handleMouseEnter}
            handleMouseLeave={handleMouseLeave}
            pathname={pathname}
          />

          {/* Mobile Menu Button - Following Figma design */}
          {isMenuOpen ? (
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-slate-100 md:hidden">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 p-0"
                onClick={() => toggleMenu()}
                aria-label="Close menu"
              >
                <Icon name="X" className="h-5 w-5" strokeWidth={2} />
              </Button>
            </div>
          ) : (
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => {
                sendEvent('menu_click_open');
                toggleMenu();
              }}
              aria-label="Open menu"
            >
              <Icon name="Menu" className="h-6 w-6" />
            </Button>
          )}
        </div>
      </div>

      {/* Mobile Navigation - Added on top of header with proper z-index */}
      <MobileNavigation isMenuOpen={isMenuOpen} toggleMenu={toggleMenu} />
    </header>
  );
}
